# Oracle Data Export Scheduler Configuration
# ==========================================

# Database Connection Settings
jdbc.url=******************************************************************************,cn=OracleContext
db.username=METS_APP
db.password=mETsaPplVl4dt91609082023

# Tables to Export (comma-separated)
tables=USER_INTAKE_FORM,QUESTION,ANSWER

# Output Directory for Export Files
output.directory=C:/Users/<USER>/Downloads/script/exports

# Schedule Settings
# How often to run the export (in days)
schedule.interval.days=7

# What time to start the export (24-hour format HH:MM)
schedule.start.time=02:00

# Cleanup Settings
# How many days to keep old export files (0 = keep all)
cleanup.keep.days=30
