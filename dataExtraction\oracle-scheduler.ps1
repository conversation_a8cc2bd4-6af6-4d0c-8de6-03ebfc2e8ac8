param(
    [Parameter(HelpMessage="Path to configuration file")]
    [string]$ConfigFile = "scheduler.properties",
    
    [Parameter(HelpMessage="Show help information")]
    [switch]$Help
)

if ($Help) {
    Write-Host "Oracle Data Export Scheduler (PowerShell)" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "This script starts the periodic data export scheduler."
    Write-Host ""
    Write-Host "Usage: .\oracle-scheduler.ps1 [-ConfigFile <path>] [-Help]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -ConfigFile    Path to configuration file (default: scheduler.properties)"
    Write-Host "  -Help          Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\oracle-scheduler.ps1"
    Write-Host "  .\oracle-scheduler.ps1 -ConfigFile my-config.properties"
    Write-Host ""
    Write-Host "Configuration File Format:"
    Write-Host "  jdbc.url=*************************************"
    Write-Host "  db.username=your_username"
    Write-Host "  db.password=your_password"
    Write-Host "  tables=TABLE1,TABLE2,TABLE3"
    Write-Host "  output.directory=C:/path/to/exports"
    Write-Host "  schedule.interval.days=7"
    Write-Host "  schedule.start.time=02:00"
    Write-Host "  cleanup.keep.days=30"
    Write-Host ""
    exit 0
}

Write-Host "Oracle Data Export Scheduler (PowerShell)" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""

Write-Host "Parameters:"
Write-Host "  Config File: $ConfigFile"
Write-Host ""

# Check if Java is available
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Java not found"
    }
    Write-Host "Java is available" -ForegroundColor Green
} catch {
    Write-Host "Error: Java is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Java and try again" -ForegroundColor Red
    exit 1
}

# Check if configuration file exists
if (-not (Test-Path $ConfigFile)) {
    Write-Host "Error: Configuration file '$ConfigFile' not found" -ForegroundColor Red
    Write-Host "Please create the configuration file or specify a different path" -ForegroundColor Red
    Write-Host ""
    Write-Host "Sample configuration file content:" -ForegroundColor Yellow
    Write-Host "jdbc.url=*************************************"
    Write-Host "db.username=your_username"
    Write-Host "db.password=your_password"
    Write-Host "tables=TABLE1,TABLE2,TABLE3"
    Write-Host "output.directory=C:/path/to/exports"
    Write-Host "schedule.interval.days=7"
    Write-Host "schedule.start.time=02:00"
    Write-Host "cleanup.keep.days=30"
    exit 1
}

# Check if scheduler class exists
if (-not (Test-Path "OracleDataScheduler.class")) {
    Write-Host "Compiling scheduler..." -ForegroundColor Yellow
    try {
        $compileResult = javac -cp ".;ojdbc8.jar" --release 8 OracleDataScheduler.java 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Compilation failed: $compileResult"
        }
        Write-Host "Compilation successful" -ForegroundColor Green
    } catch {
        Write-Host "Error compiling scheduler: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Run the scheduler
Write-Host "Starting periodic data export scheduler..." -ForegroundColor Yellow
Write-Host ""

try {
    $arguments = @(
        "-cp", ".;ojdbc8.jar",
        "OracleDataScheduler",
        $ConfigFile
    )
    
    # Start the scheduler process
    Start-Process -FilePath "java" -ArgumentList $arguments -Wait -NoNewWindow
    
} catch {
    Write-Host "Error running scheduler: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
