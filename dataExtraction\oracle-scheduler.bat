@echo off
REM Oracle Data Export Scheduler - Windows Batch Script

if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help

set CONFIG_FILE=scheduler.properties
if not "%~1"=="" set CONFIG_FILE=%~1

echo Oracle Data Export Scheduler (Windows Batch)
echo ===========================================
echo.
echo Config File: %CONFIG_FILE%
echo.

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java and try again
    exit /b 1
)
echo Java is available

REM Check if configuration file exists
if not exist "%CONFIG_FILE%" (
    echo Error: Configuration file '%CONFIG_FILE%' not found
    echo Please create the configuration file or specify a different path
    echo.
    echo Sample configuration file content:
    echo jdbc.url=*************************************
    echo db.username=your_username
    echo db.password=your_password
    echo tables=TABLE1,TABLE2,TABLE3
    echo output.directory=C:/path/to/exports
    echo schedule.interval.days=7
    echo schedule.start.time=02:00
    echo cleanup.keep.days=30
    exit /b 1
)

REM Compile scheduler if needed
if not exist "OracleDataScheduler.class" (
    echo Compiling scheduler...
    javac -cp ".;ojdbc8.jar" --release 8 OracleDataScheduler.java
    if errorlevel 1 (
        echo Error: Failed to compile scheduler
        exit /b 1
    )
    echo Compilation successful
)

REM Run the scheduler
echo Starting periodic data export scheduler...
echo.
java -cp ".;ojdbc8.jar" OracleDataScheduler "%CONFIG_FILE%"
exit /b %errorlevel%

:show_help
echo.
echo Oracle Data Export Scheduler (Windows Batch)
echo ===========================================
echo.
echo This script starts the periodic data export scheduler.
echo.
echo Usage: %~nx0 [config-file]
echo.
echo Parameters:
echo   config-file    Path to configuration file (default: scheduler.properties)
echo.
echo Examples:
echo   %~nx0
echo   %~nx0 my-config.properties
echo.
echo Configuration File Format:
echo   jdbc.url=*************************************
echo   db.username=your_username
echo   db.password=your_password
echo   tables=TABLE1,TABLE2,TABLE3
echo   output.directory=C:/path/to/exports
echo   schedule.interval.days=7
echo   schedule.start.time=02:00
echo   cleanup.keep.days=30
echo.
exit /b 0
