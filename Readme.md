How to do Data Backup?

1. From the CMD, go to dataExtraction folder -> then run the below command:

    .\oracle-export.bat -u <db username> -p <db password> -d <jdbc connection string> -t <comma-separated-tablenames> -o <output file path where the backup file needs to be saved>

Example:
    .\oracle-export.bat -u METS_APP -p "mETsaPplVl4dt91609082023" -d "******************************************************************************,cn=OracleContext" -t USER_INTAKE_FORM,QUESTION,ANSWER -o "C:\Users\<USER>\Downloads\script\output.sql"

How to do Scheduled Backup?

1. From the CMD, go to dataExtraction folder -> open the scheduler.properties file and provide the details like db username, password, connection string, table names, output directory and update the interval according to your choice (minutes/days) and enable start time if your scheduled backup interval is for days and update cleanup settings according to your choice. 

2. Now, run the command -> .\oracle-scheduler.bat

3. To Stop Scheduler -> Ctrl + C

How to restore Backedup Data?

1. From the CMD, go to dataInsertion folder -> then run the below command:

    .\oracle-insert.bat -Username <db username> -Password <db password> -JdbcUrl <jdbc connection string> -SqlFile <outfile file path where the backedup data is stored>

Example:
    .\oracle-insert.bat -Username "METS_APP" -Password "mETsaPplVl4dt91609082023" -JdbcUrl "******************************************************************************,cn=OracleContext" -SqlFile "C:\Users\<USER>\Downloads\script\output.sql"
