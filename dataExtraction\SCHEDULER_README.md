# Oracle Data Export Scheduler

## 🕒 **Periodic Data Export Feature**

This scheduler allows you to automatically export Oracle data at regular intervals (like a cron job). The existing manual export functionality remains completely intact.

## 📁 **Files Added**

- `OracleDataScheduler.java` - Main scheduler class
- `OracleDataScheduler.class` - Compiled scheduler
- `scheduler.properties` - Configuration file
- `oracle-scheduler.ps1` - PowerShell script
- `oracle-scheduler.bat` - Windows batch script

## ⚙️ **Configuration**

Edit `scheduler.properties` to configure your scheduled exports:

```properties
# Database Connection
jdbc.url=******************************************************************************,cn=OracleContext
db.username=METS_APP
db.password=mETsaPplVl4dt91609082023

# Tables to Export
tables=USER_INTAKE_FORM,QUESTION,ANSWER

# Output Directory
output.directory=C:/Users/<USER>/Downloads/script/exports

# Schedule Settings
schedule.interval.days=7        # Run every 7 days
schedule.start.time=02:00       # Start at 2:00 AM

# Cleanup Settings
cleanup.keep.days=30           # Keep files for 30 days
```

## 🚀 **How to Use**

### **Option 1: PowerShell Script (Recommended)**
```powershell
cd dataExtraction
.\oracle-scheduler.ps1
```

### **Option 2: Windows Batch Script**
```batch
cd dataExtraction
oracle-scheduler.bat
```

### **Option 3: Direct Java Command**
```bash
cd dataExtraction
java -cp ".;ojdbc8.jar" OracleDataScheduler scheduler.properties
```

## 📋 **Features**

### ✅ **Scheduling**
- Runs every N days (configurable)
- Starts at specific time (configurable)
- Calculates next run time automatically

### ✅ **File Management**
- Creates timestamped export files: `export_2025-07-07_14-30-00.sql`
- Automatically creates output directory
- Cleans up old files (configurable retention)

### ✅ **Monitoring**
- Shows next scheduled run time
- Logs all export activities with timestamps
- Displays configuration on startup

### ✅ **Error Handling**
- Continues running even if one export fails
- Detailed error logging
- Graceful shutdown with Ctrl+C

## 📊 **Example Output**

```
Oracle Data Export Scheduler
============================
Configuration loaded from: scheduler.properties
JDBC URL: jdbc:oracle:thin:@ldap://...
Username: METS_APP
Tables: USER_INTAKE_FORM,QUESTION,ANSWER
Output Directory: C:/Users/<USER>/Downloads/script/exports
Schedule: Every 7 days
Start Time: 02:00

Scheduler starting...
Next export will run in 11 hours and 30 minutes
Then every 7 days at 02:00

Press Ctrl+C to stop the scheduler

[2025-07-07_14-30-00] Starting scheduled data export...
✓ Data exported to C:/Users/<USER>/Downloads/script/exports/export_2025-07-07_14-30-00.sql
[2025-07-07_14-30-00] Export completed successfully
[2025-07-07_14-30-00] Next export scheduled for: 2025-07-14 02:00:00
```

## 🔧 **Customization**

### **Different Schedule Intervals**
```properties
schedule.interval.days=1    # Daily
schedule.interval.days=3    # Every 3 days
schedule.interval.days=14   # Bi-weekly
```

### **Different Start Times**
```properties
schedule.start.time=01:00   # 1:00 AM
schedule.start.time=23:30   # 11:30 PM
schedule.start.time=12:00   # Noon
```

### **File Retention**
```properties
cleanup.keep.days=0     # Keep all files
cleanup.keep.days=7     # Keep for 1 week
cleanup.keep.days=90    # Keep for 3 months
```

## 🛑 **Stopping the Scheduler**

- Press `Ctrl+C` to stop gracefully
- The scheduler will finish any running export before stopping
- All scheduled exports will be cancelled

## 🔄 **Coexistence with Manual Exports**

The scheduler **does not interfere** with manual exports:
- Manual exports still work exactly as before
- Use `oracle-export.ps1` or `oracle-export.bat` for manual exports
- Use `oracle-scheduler.ps1` or `oracle-scheduler.bat` for scheduled exports
- Both can run simultaneously without conflicts

## 🎯 **Production Deployment**

For production use, consider:
1. Running as a Windows Service
2. Setting up proper logging
3. Monitoring disk space in output directory
4. Backing up export files to another location
