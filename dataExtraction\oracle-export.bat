@echo off
REM Simple Oracle Data Exporter - Windows Batch Script
REM This script calls the PowerShell version for better parameter handling

if "%~1"=="" goto show_help
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help

REM Call PowerShell script with all parameters
powershell -ExecutionPolicy Bypass -File "oracle-export.ps1" %*
exit /b %errorlevel%

:show_help
echo.
echo Oracle Data Exporter (Simple Batch Version)
echo ==========================================
echo.
echo This script calls PowerShell for better parameter handling.
echo.
echo Usage: %~nx0 -Username "user" -Password "pass" -JdbcUrl "url" -Tables "table1,table2" -OutputFile "file.sql"
echo.
echo Example:
echo   %~nx0 -Username "METS_APP" -Password "password" -JdbcUrl "***************************************,cn=OracleContext" -Tables "USER_INTAKE_FORM,QUESTION,ANSWER" -OutputFile "C:\backup\data.sql"
echo.
echo Alternative: Use the PowerShell script directly:
echo   powershell -File oracle-export.ps1 -Username "user" -Password "pass" -JdbcUrl "url" -Tables "tables" -OutputFile "file"
echo.
exit /b 1
