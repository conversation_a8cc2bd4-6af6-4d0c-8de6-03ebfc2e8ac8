import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class OracleDataInserter {
    
    public static void main(String[] args) throws Exception {
        if (args.length != 4) {
            System.err.println("Usage: java OracleDataInserter "
                + "<jdbcUrl> <dbUser> <dbPass> <sqlFile>");
            System.err.println("");
            System.err.println("Parameters:");
            System.err.println("  jdbcUrl  - JDBC URL for Oracle database");
            System.err.println("  dbUser   - Database username");
            System.err.println("  dbPass   - Database password");
            System.err.println("  sqlFile  - Path to SQL file containing INSERT statements");
            System.err.println("");
            System.err.println("Example:");
            System.err.println("  java OracleDataInserter \"**********************************\" \"user\" \"pass\" \"data.sql\"");
            System.exit(1);
        }
        
        String jdbcUrl = args[0];
        String dbUser = args[1];
        String dbPass = args[2];
        String sqlFile = args[3];

        System.out.println("Oracle Data Inserter");
        System.out.println("===================");
        System.out.println("JDBC URL: " + jdbcUrl);
        System.out.println("User: " + dbUser);
        System.out.println("SQL File: " + sqlFile);
        System.out.println("");

        // Load Oracle driver
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // Set connection timeout
        DriverManager.setLoginTimeout(30);
        
        try (Connection conn = DriverManager.getConnection(jdbcUrl, dbUser, dbPass)) {
            System.out.println("✓ Connected to database successfully!");
            
            // Read and execute SQL statements
            List<String> sqlStatements = readSqlFile(sqlFile);
            System.out.println("✓ Read " + sqlStatements.size() + " SQL statements from file");
            
            if (sqlStatements.isEmpty()) {
                System.out.println("⚠ No SQL statements found in file");
                return;
            }
            
            // Execute statements
            executeStatements(conn, sqlStatements);
            
            System.out.println("");
            System.out.println("✓ Data insertion completed successfully!");
            
        } catch (SQLException e) {
            System.err.println("✗ Database connection error: " + e.getMessage());
            e.printStackTrace();
            throw e;
        } catch (IOException e) {
            System.err.println("✗ File reading error: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    private static List<String> readSqlFile(String filePath) throws IOException {
        List<String> statements = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            StringBuilder currentStatement = new StringBuilder();
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                // Skip empty lines and comments
                if (line.isEmpty() || line.startsWith("--") || line.startsWith("/*")) {
                    continue;
                }

                // Skip SET commands and other non-INSERT statements
                String upperLine = line.toUpperCase();
                if (upperLine.startsWith("SET ") ||
                    upperLine.startsWith("SPOOL ") ||
                    upperLine.equals("SET DEFINE OFF") ||
                    upperLine.equals("SET DEFINE ON") ||
                    line.equals("/")) {
                    continue;
                }
                
                currentStatement.append(line);
                
                // Check if statement is complete (ends with semicolon)
                if (line.endsWith(";")) {
                    String statement = currentStatement.toString();
                    // Remove the semicolon for execution
                    if (statement.endsWith(";")) {
                        statement = statement.substring(0, statement.length() - 1);
                    }
                    if (statement.toUpperCase().startsWith("INSERT ")) {
                        statements.add(statement);
                    }
                    currentStatement = new StringBuilder();
                }
            }
        }
        
        return statements;
    }
    
    private static void executeStatements(Connection conn, List<String> statements) throws SQLException {
        conn.setAutoCommit(false); // Use transactions
        
        int successCount = 0;
        int errorCount = 0;
        
        try (Statement stmt = conn.createStatement()) {
            System.out.println("Executing INSERT statements...");
            
            for (int i = 0; i < statements.size(); i++) {
                String sql = statements.get(i);
                
                try {
                    stmt.executeUpdate(sql);
                    successCount++;
                    
                    // Show progress every 100 statements
                    if ((i + 1) % 100 == 0) {
                        System.out.println("  Processed " + (i + 1) + " statements...");
                    }
                    
                } catch (SQLException e) {
                    errorCount++;
                    System.err.println("⚠ Error executing statement " + (i + 1) + ": " + e.getMessage());
                    System.err.println("  SQL: " + sql.substring(0, Math.min(sql.length(), 100)) + "...");
                }
            }
            
            // Commit all successful statements
            conn.commit();
            System.out.println("");
            System.out.println("Execution Summary:");
            System.out.println("  Total statements: " + statements.size());
            System.out.println("  Successful: " + successCount);
            System.out.println("  Errors: " + errorCount);
            
            if (errorCount > 0) {
                System.out.println("⚠ Some statements failed - check error messages above");
            }
            
        } catch (SQLException e) {
            conn.rollback();
            throw e;
        }
    }
}
