@echo off
REM Oracle Data Inserter - Windows Batch Script
REM This script calls the PowerShell version for better parameter handling

if "%~1"=="" goto show_help
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help

REM Call PowerShell script with all parameters
powershell -ExecutionPolicy Bypass -File "oracle-insert.ps1" %*
exit /b %errorlevel%

:show_help
echo.
echo Oracle Data Inserter (Windows Batch Version)
echo ===========================================
echo.
echo This script reads INSERT statements from a SQL file and executes them in Oracle database.
echo.
echo Usage: %~nx0 -Username "user" -Password "pass" -JdbcUrl "url" -SqlFile "file.sql"
echo.
echo Parameters:
echo   -Username    Oracle database username
echo   -Password    Oracle database password  
echo   -JdbcUrl     JDBC URL (supports LDAP URLs)
echo   -SqlFile     Path to SQL file containing INSERT statements
echo.
echo Examples:
echo   %~nx0 -Username "METS_APP" -Password "password" -JdbcUrl "**********************************" -SqlFile "data.sql"
echo.
echo   %~nx0 -Username "METS_APP" -Password "password" -JdbcUrl "***************************************,cn=OracleContext" -SqlFile "C:\backup\output.sql"
echo.
echo Alternative: Use the PowerShell script directly:
echo   powershell -File oracle-insert.ps1 -Username "user" -Password "pass" -JdbcUrl "url" -SqlFile "file.sql"
echo.
exit /b 1
