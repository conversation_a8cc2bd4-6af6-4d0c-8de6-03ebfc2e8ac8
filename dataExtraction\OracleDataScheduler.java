import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class OracleDataScheduler {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
    private ScheduledExecutorService scheduler;
    private Properties config;
    
    public static void main(String[] args) throws Exception {
        if (args.length > 0 && (args[0].equals("-h") || args[0].equals("--help"))) {
            showHelp();
            return;
        }
        
        String configFile = args.length > 0 ? args[0] : "scheduler.properties";
        
        OracleDataScheduler scheduler = new OracleDataScheduler();
        scheduler.loadConfig(configFile);
        scheduler.start();
    }
    
    private void loadConfig(String configFile) throws IOException {
        config = new Properties();
        try (FileInputStream fis = new FileInputStream(configFile)) {
            config.load(fis);
        }
        
        // Validate required properties
        String[] required = {"jdbc.url", "db.username", "db.password", "tables", "output.directory"};
        for (String prop : required) {
            if (config.getProperty(prop) == null || config.getProperty(prop).trim().isEmpty()) {
                throw new IllegalArgumentException("Required property missing: " + prop);
            }
        }
        
        System.out.println("Oracle Data Export Scheduler");
        System.out.println("============================");
        System.out.println("Configuration loaded from: " + configFile);
        System.out.println("JDBC URL: " + config.getProperty("jdbc.url"));
        System.out.println("Username: " + config.getProperty("db.username"));
        System.out.println("Tables: " + config.getProperty("tables"));
        System.out.println("Output Directory: " + config.getProperty("output.directory"));
        System.out.println("Schedule: Every " + config.getProperty("schedule.interval.days", "7") + " days");
        System.out.println("Start Time: " + config.getProperty("schedule.start.time", "02:00"));
        System.out.println("");
    }
    
    private void start() {
        scheduler = Executors.newScheduledThreadPool(1);
        
        // Get configuration
        int intervalDays = Integer.parseInt(config.getProperty("schedule.interval.days", "7"));
        String startTime = config.getProperty("schedule.start.time", "02:00");
        
        // Calculate initial delay to start at specified time
        long initialDelay = calculateInitialDelay(startTime);
        long period = intervalDays * 24 * 60 * 60; // Convert days to seconds
        
        System.out.println("Scheduler starting...");
        System.out.println("Next export will run in " + (initialDelay / 3600) + " hours and " + 
                          ((initialDelay % 3600) / 60) + " minutes");
        System.out.println("Then every " + intervalDays + " days at " + startTime);
        System.out.println("");
        System.out.println("Press Ctrl+C to stop the scheduler");
        System.out.println("");
        
        // Schedule the export task
        scheduler.scheduleAtFixedRate(this::runExport, initialDelay, period, TimeUnit.SECONDS);
        
        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\nShutting down scheduler...");
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
            }
            System.out.println("Scheduler stopped.");
        }));
        
        // Keep the main thread alive
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            System.out.println("Scheduler interrupted");
        }
    }
    
    private long calculateInitialDelay(String startTime) {
        try {
            String[] timeParts = startTime.split(":");
            int targetHour = Integer.parseInt(timeParts[0]);
            int targetMinute = Integer.parseInt(timeParts[1]);
            
            java.util.Calendar now = java.util.Calendar.getInstance();
            java.util.Calendar target = java.util.Calendar.getInstance();
            
            target.set(java.util.Calendar.HOUR_OF_DAY, targetHour);
            target.set(java.util.Calendar.MINUTE, targetMinute);
            target.set(java.util.Calendar.SECOND, 0);
            target.set(java.util.Calendar.MILLISECOND, 0);
            
            // If target time has passed today, schedule for tomorrow
            if (target.before(now)) {
                target.add(java.util.Calendar.DAY_OF_MONTH, 1);
            }
            
            return (target.getTimeInMillis() - now.getTimeInMillis()) / 1000;
        } catch (Exception e) {
            System.err.println("Invalid start time format: " + startTime + ". Using 1 minute delay.");
            return 60; // Default to 1 minute if parsing fails
        }
    }
    
    private void runExport() {
        String timestamp = DATE_FORMAT.format(new Date());
        System.out.println("[" + timestamp + "] Starting scheduled data export...");
        
        try {
            // Prepare parameters for OracleDataExporter
            String jdbcUrl = config.getProperty("jdbc.url");
            String username = config.getProperty("db.username");
            String password = config.getProperty("db.password");
            String tables = config.getProperty("tables");
            String outputDir = config.getProperty("output.directory");
            String outputFile = outputDir + "/export_" + timestamp + ".sql";
            
            // Create output directory if it doesn't exist
            java.io.File dir = new java.io.File(outputDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // Run the export
            String[] exportArgs = {jdbcUrl, username, password, tables, outputFile};
            OracleDataExporter.main(exportArgs);
            
            System.out.println("[" + timestamp + "] Export completed successfully: " + outputFile);
            
            // Optional: Clean up old files
            cleanupOldFiles(outputDir);
            
        } catch (Exception e) {
            System.err.println("[" + timestamp + "] Export failed: " + e.getMessage());
            e.printStackTrace();
        }
        
        // Calculate next run time
        int intervalDays = Integer.parseInt(config.getProperty("schedule.interval.days", "7"));
        java.util.Calendar nextRun = java.util.Calendar.getInstance();
        nextRun.add(java.util.Calendar.DAY_OF_MONTH, intervalDays);
        System.out.println("[" + timestamp + "] Next export scheduled for: " + 
                          new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(nextRun.getTime()));
        System.out.println("");
    }
    
    private void cleanupOldFiles(String outputDir) {
        try {
            int keepDays = Integer.parseInt(config.getProperty("cleanup.keep.days", "30"));
            if (keepDays <= 0) return;
            
            java.io.File dir = new java.io.File(outputDir);
            java.io.File[] files = dir.listFiles((d, name) -> name.startsWith("export_") && name.endsWith(".sql"));
            
            if (files != null) {
                long cutoffTime = System.currentTimeMillis() - (keepDays * 24L * 60L * 60L * 1000L);
                int deletedCount = 0;
                
                for (java.io.File file : files) {
                    if (file.lastModified() < cutoffTime) {
                        if (file.delete()) {
                            deletedCount++;
                        }
                    }
                }
                
                if (deletedCount > 0) {
                    System.out.println("Cleaned up " + deletedCount + " old export files (older than " + keepDays + " days)");
                }
            }
        } catch (Exception e) {
            System.err.println("Warning: Failed to cleanup old files: " + e.getMessage());
        }
    }
    
    private static void showHelp() {
        System.out.println("Oracle Data Export Scheduler");
        System.out.println("============================");
        System.out.println("");
        System.out.println("Usage: java OracleDataScheduler [config-file]");
        System.out.println("");
        System.out.println("Parameters:");
        System.out.println("  config-file    Path to configuration file (default: scheduler.properties)");
        System.out.println("");
        System.out.println("The configuration file should contain:");
        System.out.println("  jdbc.url=*************************************");
        System.out.println("  db.username=your_username");
        System.out.println("  db.password=your_password");
        System.out.println("  tables=TABLE1,TABLE2,TABLE3");
        System.out.println("  output.directory=C:/path/to/exports");
        System.out.println("  schedule.interval.days=7");
        System.out.println("  schedule.start.time=02:00");
        System.out.println("  cleanup.keep.days=30");
        System.out.println("");
        System.out.println("Example:");
        System.out.println("  java OracleDataScheduler my-config.properties");
    }
}
