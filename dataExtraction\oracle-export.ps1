param(
    [Parameter(Mandatory=$true, HelpMessage="Oracle username")]
    [string]$Username,
    
    [Parameter(Mandatory=$true, HelpMessage="Oracle password")]
    [string]$Password,
    
    [Parameter(Mandatory=$true, HelpMessage="JDBC URL")]
    [string]$JdbcUrl,
    
    [Parameter(Mandatory=$true, HelpMessage="Comma-separated list of tables")]
    [string]$Tables,
    
    [Parameter(Mandatory=$true, HelpMessage="Output file path")]
    [string]$OutputFile
)

Write-Host "Oracle Data Exporter (PowerShell)" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

Write-Host "Parameters:"
Write-Host "  User: $Username"
Write-Host "  JDBC URL: $JdbcUrl"
Write-Host "  Tables: $Tables"
Write-Host "  Output: $OutputFile"
Write-Host ""

# Check if Java is available
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Java not found"
    }
    Write-Host "Java is available" -ForegroundColor Green
} catch {
    Write-Host "Error: Java is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Java and try again" -ForegroundColor Red
    exit 1
}

# Check if JAR file exists
if (-not (Test-Path "oracle-data-exporter.jar")) {
    Write-Host "Error: oracle-data-exporter.jar not found" -ForegroundColor Red
    Write-Host "Please ensure the JAR file is in the same directory as this script" -ForegroundColor Red
    exit 1
}

# Check if Oracle JDBC driver exists
if (-not (Test-Path "ojdbc8.jar")) {
    Write-Host "Error: ojdbc8.jar not found" -ForegroundColor Red
    Write-Host "Please ensure the Oracle JDBC driver is in the same directory as this script" -ForegroundColor Red
    exit 1
}

# Run the Java application
Write-Host "Starting data extraction..." -ForegroundColor Yellow

try {
    $arguments = @(
        "-cp", "oracle-data-exporter.jar;ojdbc8.jar",
        "OracleDataExporter",
        "`"$JdbcUrl`"",
        "`"$Username`"",
        "`"$Password`"",
        "`"$Tables`"",
        "`"$OutputFile`""
    )
    
    $process = Start-Process -FilePath "java" -ArgumentList $arguments -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "Data extraction completed successfully!" -ForegroundColor Green
        Write-Host "Output written to: $OutputFile" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "Data extraction failed!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Error running Java application: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
