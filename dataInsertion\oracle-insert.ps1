param(
    [Parameter(Mandatory=$true, HelpMessage="Oracle username")]
    [string]$Username,
    
    [Parameter(Mandatory=$true, HelpMessage="Oracle password")]
    [string]$Password,
    
    [Parameter(Mandatory=$true, HelpMessage="JDBC URL")]
    [string]$JdbcUrl,
    
    [Parameter(Mandatory=$true, HelpMessage="SQL file path containing INSERT statements")]
    [string]$SqlFile
)

Write-Host "Oracle Data Inserter (PowerShell)" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

Write-Host "Parameters:"
Write-Host "  User: $Username"
Write-Host "  JDBC URL: $JdbcUrl"
Write-Host "  SQL File: $SqlFile"
Write-Host ""

# Check if Java is available
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Java not found"
    }
    Write-Host "Java is available" -ForegroundColor Green
} catch {
    Write-Host "Error: Java is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Java and try again" -ForegroundColor Red
    exit 1
}

# Check if JAR file exists
if (-not (Test-Path "oracle-data-inserter.jar")) {
    Write-Host "Error: oracle-data-inserter.jar not found" -ForegroundColor Red
    Write-Host "Please ensure the JAR file is in the same directory as this script" -ForegroundColor Red
    exit 1
}

# Check if Oracle JDBC driver exists
if (-not (Test-Path "ojdbc8.jar")) {
    Write-Host "Error: ojdbc8.jar not found" -ForegroundColor Red
    Write-Host "Please ensure the Oracle JDBC driver is in the same directory as this script" -ForegroundColor Red
    exit 1
}

# Check if SQL file exists
$resolvedSqlFile = Resolve-Path $SqlFile -ErrorAction SilentlyContinue
if (-not $resolvedSqlFile) {
    Write-Host "Error: SQL file '$SqlFile' not found" -ForegroundColor Red
    Write-Host "Please ensure the SQL file exists and the path is correct" -ForegroundColor Red
    exit 1
}
$SqlFile = $resolvedSqlFile.Path

# Run the Java application
Write-Host "Starting data insertion..." -ForegroundColor Yellow

try {
    $arguments = @(
        "-cp", "oracle-data-inserter.jar;ojdbc8.jar",
        "OracleDataInserter",
        "`"$JdbcUrl`"",
        "`"$Username`"",
        "`"$Password`"",
        "`"$SqlFile`""
    )
    
    $process = Start-Process -FilePath "java" -ArgumentList $arguments -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "Data insertion completed successfully!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "Data insertion failed!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Error running Java application: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
