import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;

public class OracleDataExporter {
    private static final SimpleDateFormat DATE_FMT = 
        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static void main(String[] args) throws Exception {
        if (args.length != 5) {
            System.err.println("Usage: java OracleDataExporter "
                + "<jdbcUrl> <dbUser> <dbPass> <table1,table2,...> <outputFile>");
            System.exit(1);
        }
        String jdbcUrl     = args[0];
        String dbUser      = args[1];
        String dbPass      = args[2];
        String[] tables    = args[3].split(",");
        String outputFile  = args[4];

        // 1) Load Oracle driver (ensure ojdbc jar on classpath)
        Class.forName("oracle.jdbc.driver.OracleDriver");

        // Set connection timeout
        DriverManager.setLoginTimeout(30);

        try (Connection conn = DriverManager.getConnection(jdbcUrl, dbUser, dbPass);
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile)))
        {
            for (String table : tables) {
                writer.write("SET DEFINE OFF");
                writer.newLine();
                dumpTable(conn, table.trim(), writer);
            }
            System.out.println("✔ Data exported to " + outputFile);
        }
    }

    private static void dumpTable(Connection conn, String table, BufferedWriter out)
            throws SQLException, IOException
    {
        String sql = "SELECT * FROM " + table;
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql))
        {
            ResultSetMetaData md = rs.getMetaData();
            int colCount = md.getColumnCount();

            while (rs.next()) {
                StringBuilder vals = new StringBuilder();
                // Get metadata once for all columns
                ResultSetMetaData metaData = rs.getMetaData();

                for (int i = 1; i <= colCount; i++) {
                    Object obj = rs.getObject(i);
                    if (obj == null) {
                        vals.append("NULL");
                    }
                    else if (obj instanceof Number) {
                        vals.append(obj.toString());
                    }
                    else {
                        // Check column type for date handling
                        int columnType = metaData.getColumnType(i);
                        String columnTypeName = metaData.getColumnTypeName(i).toUpperCase();

                        if (columnType == Types.DATE || columnType == Types.TIMESTAMP ||
                            columnTypeName.contains("DATE") || columnTypeName.contains("TIMESTAMP") ||
                            obj instanceof java.sql.Date || obj instanceof java.sql.Timestamp) {
                            // Handle as date/timestamp
                            try {
                                Timestamp ts = rs.getTimestamp(i);
                                if (ts != null) {
                                    String dateStr = DATE_FMT.format(ts);
                                    vals.append("TO_DATE('")
                                        .append(dateStr)
                                        .append("','YYYY-MM-DD HH24:MI:SS')");
                                } else {
                                    vals.append("NULL");
                                }
                            } catch (SQLException e) {
                                // If timestamp conversion fails, treat as string
                                String s = rs.getString(i);
                                if (s != null) {
                                    s = s.replace("'", "''");
                                    vals.append("'").append(s).append("'");
                                } else {
                                    vals.append("NULL");
                                }
                            }
                        } else {
                            // Handle as string
                            String s = rs.getString(i);
                            if (s != null) {
                                s = s.replace("'", "''");
                                vals.append("'").append(s).append("'");
                            } else {
                                vals.append("NULL");
                            }
                        }
                    }
                    if (i < colCount) vals.append(", ");
                }
                out.write("INSERT INTO " + table
                        + " VALUES (" + vals + ");");
                out.newLine();
            }
        }
    }
}
