# Test Configuration for 5-Minute Scheduler
# ==========================================

# Database Connection Settings
jdbc.url=******************************************************************************,cn=OracleContext
db.username=METS_APP
db.password=mETsaPplVl4dt91609082023

# Tables to Export (comma-separated)
tables=USER_INTAKE_FORM

# Output Directory for Export Files
output.directory=C:/Users/<USER>/Downloads/script/exports

# Schedule Settings - 5 MINUTES
schedule.interval.type=minutes
schedule.interval.value=5

# Cleanup Settings - Keep files for 1 day only
cleanup.keep.days=1
